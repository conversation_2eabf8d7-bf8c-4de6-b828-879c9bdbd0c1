#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件拆分脚本
将陈怡桥.txt文件按每500行拆分成多个小文件
"""

import os

def split_file(input_file, lines_per_file=500):
    """
    将大文件按指定行数拆分成多个小文件

    Args:
        input_file (str): 输入文件路径
        lines_per_file (int): 每个文件的行数，默认500行
    """

    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在！")
        return

    # 获取文件名（不含扩展名）
    base_name = os.path.splitext(os.path.basename(input_file))[0]

    try:
        # 首先读取所有行
        with open(input_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()

        total_lines = len(all_lines)
        print(f"开始拆分文件：{input_file}")
        print(f"文件总行数：{total_lines}")
        print(f"每个文件包含 {lines_per_file} 行")
        print("-" * 50)

        file_number = 1
        start_index = 0

        while start_index < total_lines:
            # 计算当前文件的结束索引
            end_index = min(start_index + lines_per_file, total_lines)
            current_file_lines = end_index - start_index

            # 创建输出文件名
            output_filename = f"{base_name}-{file_number}.txt"

            # 写入当前文件
            with open(output_filename, 'w', encoding='utf-8') as output_file:
                for i in range(start_index, end_index):
                    output_file.write(all_lines[i])

            print(f"已完成：{output_filename} ({current_file_lines} 行)")

            # 更新索引和文件编号
            start_index = end_index
            file_number += 1

        print("-" * 50)
        print(f"拆分完成！")
        print(f"总共处理了 {total_lines} 行")
        print(f"生成了 {file_number - 1} 个文件")

        # 验证拆分结果
        print("\n验证拆分结果：")
        total_split_lines = 0
        for i in range(1, file_number):
            filename = f"{base_name}-{i}.txt"
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    lines_count = len(f.readlines())
                total_split_lines += lines_count
                print(f"{filename}: {lines_count} 行")

        print(f"验证：原文件 {total_lines} 行，拆分后总计 {total_split_lines} 行")
        if total_lines == total_split_lines:
            print("✓ 拆分验证成功！")
        else:
            print("✗ 拆分验证失败！")

    except Exception as e:
        print(f"处理文件时发生错误：{e}")

if __name__ == "__main__":
    # 拆分陈怡桥.txt文件
    input_file = "陈怡桥.txt"
    split_file(input_file, 5000)
